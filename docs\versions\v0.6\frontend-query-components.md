# AI-FDB v0.6 - 前端查询组件

## 概述

前端查询组件模块提供完整的AI查询用户界面，包括自然语言输入、查询结果展示、图表可视化、历史管理等核心功能。所有组件基于Vue 3和Element Plus构建，提供智能化的查询体验和直观的数据展示。

## 🎯 设计目标

- **智能查询** - 自然语言查询界面，降低使用门槛
- **实时反馈** - 查询过程的实时状态和进度反馈
- **可视化展示** - 智能图表推荐和交互式数据展示
- **历史管理** - 查询历史的保存、搜索和重用
- **协作分享** - 查询结果的分享和协作功能
- **响应式设计** - 适配各种设备和屏幕尺寸

## 🏗️ 组件架构

### 组件层次结构
```
QueryWorkspace (查询工作空间)
├── QueryInput (查询输入器)
│   ├── NaturalLanguageInput (自然语言输入)
│   ├── QuerySuggestions (查询建议)
│   └── QueryHistory (查询历史)
├── QueryProcessor (查询处理器)
│   ├── ProcessingIndicator (处理指示器)
│   ├── ProgressTracker (进度跟踪器)
│   └── ErrorHandler (错误处理器)
├── ResultViewer (结果查看器)
│   ├── DataTable (数据表格)
│   ├── ChartContainer (图表容器)
│   └── ExportManager (导出管理器)
└── QueryManager (查询管理器)
    ├── SessionManager (会话管理器)
    ├── HistoryViewer (历史查看器)
    └── ShareManager (分享管理器)
```

## 🎨 核心组件

### 1. QueryInput - 查询输入器
智能的自然语言查询输入组件。

```vue
<template>
  <div class="query-input">
    <el-card header="AI智能查询">
      <!-- 查询输入区域 -->
      <div class="input-area">
        <el-input
          v-model="queryText"
          type="textarea"
          :rows="4"
          placeholder="请用自然语言描述您的查询需求，例如：显示最近一个月的销售数据"
          @keydown.ctrl.enter="executeQuery"
          @input="handleInputChange"
          show-word-limit
          maxlength="500"
        />
        
        <!-- 查询建议 -->
        <div class="query-suggestions" v-if="suggestions.length > 0">
          <h5>查询建议</h5>
          <div class="suggestion-list">
            <el-tag
              v-for="suggestion in suggestions"
              :key="suggestion.id"
              @click="applySuggestion(suggestion)"
              class="suggestion-tag"
              type="info"
            >
              {{ suggestion.text }}
            </el-tag>
          </div>
        </div>
        
        <!-- 快速查询模板 -->
        <div class="quick-templates">
          <h5>快速查询</h5>
          <el-button-group>
            <el-button
              v-for="template in quickTemplates"
              :key="template.id"
              @click="applyTemplate(template)"
              size="small"
            >
              {{ template.name }}
            </el-button>
          </el-button-group>
        </div>
      </div>
      
      <!-- 查询配置 -->
      <div class="query-config">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="AI模型">
              <el-select v-model="queryConfig.aiModel" size="small">
                <el-option label="通义千问-turbo" value="qwen-turbo" />
                <el-option label="通义千问-plus" value="qwen-plus" />
                <el-option label="GPT-3.5-turbo" value="gpt-3.5-turbo" />
                <el-option label="GPT-4" value="gpt-4" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="结果限制">
              <el-input-number
                v-model="queryConfig.maxResults"
                :min="10"
                :max="10000"
                size="small"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="超时时间">
              <el-select v-model="queryConfig.timeout" size="small">
                <el-option label="30秒" :value="30" />
                <el-option label="60秒" :value="60" />
                <el-option label="120秒" :value="120" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      
      <!-- 操作按钮 -->
      <div class="query-actions">
        <el-button
          @click="executeQuery"
          type="primary"
          :loading="querying"
          :disabled="!queryText.trim()"
        >
          <el-icon><Search /></el-icon>
          执行查询
        </el-button>
        <el-button @click="clearQuery">
          <el-icon><Delete /></el-icon>
          清空
        </el-button>
        <el-button @click="showHistory = true">
          <el-icon><Clock /></el-icon>
          历史记录
        </el-button>
        <el-button @click="saveAsTemplate" :disabled="!queryText.trim()">
          <el-icon><Star /></el-icon>
          保存模板
        </el-button>
      </div>
    </el-card>
    
    <!-- 查询历史对话框 -->
    <QueryHistoryDialog
      v-model="showHistory"
      @select-query="applyHistoryQuery"
    />
  </div>
</template>

<script>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Delete, Clock, Star } from '@element-plus/icons-vue'
import QueryHistoryDialog from './QueryHistoryDialog.vue'

export default {
  name: 'QueryInput',
  components: {
    Search,
    Delete,
    Clock,
    Star,
    QueryHistoryDialog
  },
  emits: ['query-executed'],
  setup(props, { emit }) {
    const queryText = ref('')
    const querying = ref(false)
    const showHistory = ref(false)
    const suggestions = ref([])
    const quickTemplates = ref([])
    
    const queryConfig = reactive({
      aiModel: 'qwen-turbo',
      maxResults: 1000,
      timeout: 30
    })
    
    const handleInputChange = debounce(async (value) => {
      if (value.length > 10) {
        await loadSuggestions(value)
      } else {
        suggestions.value = []
      }
    }, 500)
    
    const loadSuggestions = async (query) => {
      try {
        const response = await api.post('/ai/query/suggestions', { query })
        suggestions.value = response.data
      } catch (error) {
        console.warn('获取查询建议失败', error)
      }
    }
    
    const executeQuery = async () => {
      if (!queryText.value.trim()) {
        ElMessage.warning('请输入查询内容')
        return
      }
      
      querying.value = true
      try {
        const response = await api.post('/ai/query/execute', {
          question: queryText.value,
          config: queryConfig
        })
        
        emit('query-executed', {
          query: queryText.value,
          result: response.data
        })
        
        ElMessage.success('查询执行成功')
        
      } catch (error) {
        ElMessage.error('查询执行失败: ' + error.message)
      } finally {
        querying.value = false
      }
    }
    
    const applySuggestion = (suggestion) => {
      queryText.value = suggestion.text
      suggestions.value = []
    }
    
    const applyTemplate = (template) => {
      queryText.value = template.query
    }
    
    const clearQuery = () => {
      queryText.value = ''
      suggestions.value = []
    }
    
    return {
      queryText,
      querying,
      showHistory,
      suggestions,
      quickTemplates,
      queryConfig,
      handleInputChange,
      executeQuery,
      applySuggestion,
      applyTemplate,
      clearQuery
    }
  }
}
</script>
```

### 2. ResultViewer - 结果查看器
展示查询结果和可视化图表的组件。

```vue
<template>
  <div class="result-viewer">
    <el-card v-if="queryResult">
      <!-- 结果头部信息 -->
      <template #header>
        <div class="result-header">
          <div class="header-left">
            <h3>查询结果</h3>
            <div class="result-meta">
              <el-tag size="small">
                {{ queryResult.rowCount }} 条记录
              </el-tag>
              <el-tag size="small" type="info">
                执行时间: {{ queryResult.executionTime }}ms
              </el-tag>
              <el-tag 
                size="small" 
                :type="getConfidenceType(queryResult.confidence)"
              >
                置信度: {{ (queryResult.confidence * 100).toFixed(1) }}%
              </el-tag>
            </div>
          </div>
          <div class="header-actions">
            <el-button-group>
              <el-button
                @click="viewMode = 'table'"
                :type="viewMode === 'table' ? 'primary' : 'default'"
                size="small"
              >
                <el-icon><Grid /></el-icon>
                表格
              </el-button>
              <el-button
                @click="viewMode = 'chart'"
                :type="viewMode === 'chart' ? 'primary' : 'default'"
                size="small"
              >
                <el-icon><TrendCharts /></el-icon>
                图表
              </el-button>
            </el-button-group>
          </div>
        </div>
      </template>
      
      <!-- 生成的SQL展示 -->
      <div class="sql-display" v-if="queryResult.generatedSQL">
        <el-collapse>
          <el-collapse-item title="查看生成的SQL" name="sql">
            <div class="sql-content">
              <pre><code>{{ queryResult.generatedSQL }}</code></pre>
              <el-button @click="copySQLToClipboard" size="small" type="text">
                <el-icon><CopyDocument /></el-icon>
                复制SQL
              </el-button>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
      
      <!-- 表格视图 -->
      <div class="table-view" v-if="viewMode === 'table'">
        <el-table
          :data="paginatedData"
          v-loading="loading"
          height="500"
          style="width: 100%"
        >
          <el-table-column
            v-for="column in queryResult.columns"
            :key="column.name"
            :prop="column.name"
            :label="column.name"
            :width="getColumnWidth(column)"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <span :class="getCellClass(column, row[column.name])">
                {{ formatCellValue(column, row[column.name]) }}
              </span>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 分页 -->
        <div class="pagination">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="queryResult.rowCount"
            :page-sizes="[20, 50, 100, 200]"
            layout="total, sizes, prev, pager, next, jumper"
          />
        </div>
      </div>
      
      <!-- 图表视图 -->
      <div class="chart-view" v-if="viewMode === 'chart'">
        <ChartContainer
          :query-result="queryResult"
          @chart-generated="handleChartGenerated"
        />
      </div>
      
      <!-- 操作按钮 -->
      <div class="result-actions">
        <el-button @click="exportData('excel')" size="small">
          <el-icon><Download /></el-icon>
          导出Excel
        </el-button>
        <el-button @click="exportData('csv')" size="small">
          <el-icon><Download /></el-icon>
          导出CSV
        </el-button>
        <el-button @click="shareResult" size="small">
          <el-icon><Share /></el-icon>
          分享结果
        </el-button>
        <el-button @click="saveToWorkspace" size="small">
          <el-icon><FolderAdd /></el-icon>
          保存到工作空间
        </el-button>
      </div>
      
      <!-- 用户反馈 -->
      <div class="user-feedback">
        <el-divider>查询反馈</el-divider>
        <div class="feedback-section">
          <span>查询结果是否准确？</span>
          <el-rate
            v-model="feedback.rating"
            @change="submitFeedback"
            show-text
            :texts="['很差', '较差', '一般', '较好', '很好']"
          />
          <el-input
            v-model="feedback.comments"
            placeholder="请提供改进建议..."
            type="textarea"
            :rows="2"
            style="margin-top: 10px;"
          />
          <el-button @click="submitFeedback" size="small" style="margin-top: 10px;">
            提交反馈
          </el-button>
        </div>
      </div>
    </el-card>
    
    <el-empty v-else description="暂无查询结果" />
  </div>
</template>

<script>
import { ref, reactive, computed } from 'vue'
import ChartContainer from './ChartContainer.vue'

export default {
  name: 'ResultViewer',
  components: { ChartContainer },
  props: {
    queryResult: {
      type: Object,
      default: null
    }
  },
  setup(props) {
    const viewMode = ref('table')
    const loading = ref(false)
    const currentPage = ref(1)
    const pageSize = ref(50)
    
    const feedback = reactive({
      rating: 0,
      comments: ''
    })
    
    const paginatedData = computed(() => {
      if (!props.queryResult?.rows) return []
      
      const start = (currentPage.value - 1) * pageSize.value
      const end = start + pageSize.value
      return props.queryResult.rows.slice(start, end)
    })
    
    const getConfidenceType = (confidence) => {
      if (confidence >= 0.8) return 'success'
      if (confidence >= 0.6) return 'warning'
      return 'danger'
    }
    
    const formatCellValue = (column, value) => {
      if (value === null || value === undefined) return '-'
      
      if (column.type === 'number') {
        return Number(value).toLocaleString()
      }
      
      if (column.type === 'date') {
        return new Date(value).toLocaleDateString()
      }
      
      return String(value)
    }
    
    const exportData = async (format) => {
      try {
        const response = await api.post('/ai/query/export', {
          queryId: props.queryResult.id,
          format: format
        })
        
        // 下载文件
        const blob = new Blob([response.data])
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `query_result.${format}`
        link.click()
        window.URL.revokeObjectURL(url)
        
      } catch (error) {
        ElMessage.error('导出失败: ' + error.message)
      }
    }
    
    const submitFeedback = async () => {
      if (feedback.rating === 0) {
        ElMessage.warning('请先评分')
        return
      }
      
      try {
        await api.post('/ai/query/feedback', {
          queryId: props.queryResult.id,
          rating: feedback.rating,
          comments: feedback.comments
        })
        
        ElMessage.success('反馈提交成功')
        
      } catch (error) {
        ElMessage.error('反馈提交失败')
      }
    }
    
    return {
      viewMode,
      loading,
      currentPage,
      pageSize,
      feedback,
      paginatedData,
      getConfidenceType,
      formatCellValue,
      exportData,
      submitFeedback
    }
  }
}
</script>
```

### 3. QueryHistoryDialog - 查询历史对话框
管理和重用历史查询的组件。

```vue
<template>
  <el-dialog v-model="visible" title="查询历史" width="800px">
    <!-- 搜索和筛选 -->
    <div class="history-filters">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-input
            v-model="searchText"
            placeholder="搜索历史查询..."
            prefix-icon="Search"
            @input="handleSearch"
            clearable
          />
        </el-col>
        <el-col :span="6">
          <el-select v-model="statusFilter" placeholder="状态筛选">
            <el-option label="全部" value="" />
            <el-option label="成功" value="completed" />
            <el-option label="失败" value="failed" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            placeholder="选择日期范围"
            size="default"
          />
        </el-col>
      </el-row>
    </div>
    
    <!-- 历史记录列表 -->
    <div class="history-list">
      <el-table :data="filteredHistory" height="400">
        <el-table-column prop="originalQuestion" label="查询问题" min-width="200">
          <template #default="{ row }">
            <div class="question-cell">
              <p class="question-text">{{ row.originalQuestion }}</p>
              <el-tag size="small" :type="getStatusType(row.status)">
                {{ getStatusText(row.status) }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="confidenceScore" label="置信度" width="100">
          <template #default="{ row }">
            <el-tag :type="getConfidenceType(row.confidenceScore)">
              {{ (row.confidenceScore * 100).toFixed(1) }}%
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="executionTime" label="执行时间" width="100">
          <template #default="{ row }">
            {{ row.executionTime }}ms
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="查询时间" width="150">
          <template #default="{ row }">
            {{ formatDateTime(row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150">
          <template #default="{ row }">
            <el-button @click="selectQuery(row)" size="small" type="text">
              重用
            </el-button>
            <el-button @click="viewDetails(row)" size="small" type="text">
              详情
            </el-button>
            <el-button @click="deleteQuery(row)" size="small" type="text" class="danger">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <!-- 分页 -->
    <div class="history-pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        layout="total, prev, pager, next"
      />
    </div>
  </el-dialog>
</template>

<script>
import { ref, computed, watch } from 'vue'

export default {
  name: 'QueryHistoryDialog',
  props: {
    modelValue: Boolean
  },
  emits: ['update:modelValue', 'select-query'],
  setup(props, { emit }) {
    const visible = computed({
      get: () => props.modelValue,
      set: (value) => emit('update:modelValue', value)
    })
    
    const historyData = ref([])
    const searchText = ref('')
    const statusFilter = ref('')
    const dateRange = ref([])
    const currentPage = ref(1)
    const pageSize = ref(20)
    const total = ref(0)
    
    const filteredHistory = computed(() => {
      let filtered = historyData.value
      
      if (searchText.value) {
        filtered = filtered.filter(item =>
          item.originalQuestion.toLowerCase().includes(searchText.value.toLowerCase())
        )
      }
      
      if (statusFilter.value) {
        filtered = filtered.filter(item => item.status === statusFilter.value)
      }
      
      if (dateRange.value && dateRange.value.length === 2) {
        const [start, end] = dateRange.value
        filtered = filtered.filter(item => {
          const itemDate = new Date(item.createdAt)
          return itemDate >= start && itemDate <= end
        })
      }
      
      return filtered
    })
    
    const loadHistory = async () => {
      try {
        const response = await api.get('/ai/query/history', {
          params: {
            page: currentPage.value - 1,
            size: pageSize.value,
            search: searchText.value,
            status: statusFilter.value,
            startDate: dateRange.value?.[0],
            endDate: dateRange.value?.[1]
          }
        })
        
        historyData.value = response.data.content
        total.value = response.data.totalElements
        
      } catch (error) {
        ElMessage.error('加载历史记录失败')
      }
    }
    
    const selectQuery = (query) => {
      emit('select-query', query)
      visible.value = false
    }
    
    watch(visible, (newValue) => {
      if (newValue) {
        loadHistory()
      }
    })
    
    return {
      visible,
      historyData,
      searchText,
      statusFilter,
      dateRange,
      currentPage,
      pageSize,
      total,
      filteredHistory,
      loadHistory,
      selectQuery
    }
  }
}
</script>
```

## 🎨 样式设计

### 主题配色
```scss
// 查询相关色彩
$query-primary: #409EFF;
$query-success: #67C23A;
$query-warning: #E6A23C;
$query-danger: #F56C6C;

// 置信度色彩
$confidence-high: #67C23A;
$confidence-medium: #E6A23C;
$confidence-low: #F56C6C;

// 状态色彩
$status-completed: #67C23A;
$status-processing: #409EFF;
$status-failed: #F56C6C;
```

### 响应式布局
```scss
.query-workspace {
  @include mobile {
    .query-input {
      .input-area {
        padding: 10px;
      }
      
      .query-config {
        .el-col {
          margin-bottom: 10px;
        }
      }
    }
    
    .result-viewer {
      .el-table {
        font-size: 12px;
      }
    }
  }
}
```

## 🧪 测试用例

### 组件测试
- 查询输入组件测试
- 结果展示组件测试
- 历史管理组件测试
- 图表容器组件测试
- 响应式布局测试

### 交互测试
- 自然语言输入测试
- 查询执行流程测试
- 结果可视化测试
- 历史查询重用测试
- 分享和导出测试

### 性能测试
- 大数据量渲染测试
- 查询响应时间测试
- 组件切换性能测试
- 内存使用优化测试

---

**相关文档**:
- [AI查询引擎](./ai-query-engine.md)
- [MCP数据库服务](./mcp-database-service.md)
- [数据可视化系统](./data-visualization-system.md)
- [训练数据管理](./training-data-management.md)
