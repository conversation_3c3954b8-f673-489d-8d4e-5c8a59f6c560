# AI-FDB v0.6 - 数据可视化系统

## 概述

数据可视化系统基于ECharts和D3.js构建，为查询结果提供智能图表推荐和交互式可视化展示。系统能够根据数据类型和结构自动推荐最适合的图表类型，支持多种图表格式，并提供丰富的交互功能。

## 🎯 功能目标

- **智能图表推荐** - 根据数据特征自动推荐最适合的图表类型
- **多样化图表** - 支持柱状图、折线图、饼图、散点图、热力图等
- **交互式展示** - 缩放、筛选、钻取、联动等交互功能
- **自定义配置** - 支持图表样式、颜色、标签的自定义
- **导出功能** - 支持PNG、SVG、PDF等格式的图表导出
- **响应式设计** - 适配不同屏幕尺寸和设备

## 🏗️ 系统架构

### 可视化流程
```
查询结果 → 数据分析 → 图表推荐 → 图表生成 → 交互配置 → 渲染展示 → 导出保存
    ↓        ↓        ↓        ↓        ↓        ↓        ↓
  数据预处理  特征提取  推荐算法  图表配置  交互绑定  DOM渲染  格式转换
```

### 核心组件
1. **DataAnalyzer** - 数据分析器
2. **ChartRecommender** - 图表推荐器
3. **ChartGenerator** - 图表生成器
4. **InteractionManager** - 交互管理器
5. **ExportManager** - 导出管理器
6. **ThemeManager** - 主题管理器

## 🔧 技术实现

### 可视化配置
```yaml
# application.yml
visualization:
  # 图表库配置
  charts:
    default-library: echarts
    libraries:
      echarts:
        version: 5.4.3
        cdn: https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js
        themes: [default, dark, vintage, westeros, essos, wonderland]
        
      d3:
        version: 7.8.5
        cdn: https://cdn.jsdelivr.net/npm/d3@7.8.5/dist/d3.min.js
        
    # 图表推荐配置
    recommendation:
      enable-auto-recommendation: true
      confidence-threshold: 0.8
      max-recommendations: 5
      
    # 导出配置
    export:
      formats: [png, svg, pdf, excel]
      max-width: 4096
      max-height: 4096
      quality: 0.9
      
    # 性能配置
    performance:
      max-data-points: 10000
      enable-data-sampling: true
      sampling-threshold: 5000
      render-timeout: 30000
```

### 数据分析器
```java
@Service
public class DataAnalyzer {

    public DataAnalysisResult analyzeQueryResult(QueryExecutionResult queryResult) {
        List<Map<String, Object>> rows = queryResult.getRows();
        List<ColumnMetadata> columns = queryResult.getColumns();
        
        if (rows.isEmpty() || columns.isEmpty()) {
            return DataAnalysisResult.empty();
        }
        
        // 1. 分析列类型
        Map<String, ColumnType> columnTypes = analyzeColumnTypes(columns, rows);
        
        // 2. 分析数据分布
        Map<String, DataDistribution> distributions = analyzeDataDistributions(rows, columnTypes);
        
        // 3. 检测时间序列
        TimeSeriesInfo timeSeriesInfo = detectTimeSeries(columns, rows);
        
        // 4. 分析数据关系
        DataRelationship relationships = analyzeDataRelationships(rows, columnTypes);
        
        // 5. 计算数据统计
        DataStatistics statistics = calculateStatistics(rows, columnTypes);
        
        return DataAnalysisResult.builder()
            .columnTypes(columnTypes)
            .distributions(distributions)
            .timeSeriesInfo(timeSeriesInfo)
            .relationships(relationships)
            .statistics(statistics)
            .rowCount(rows.size())
            .columnCount(columns.size())
            .build();
    }

    private Map<String, ColumnType> analyzeColumnTypes(List<ColumnMetadata> columns, List<Map<String, Object>> rows) {
        Map<String, ColumnType> types = new HashMap<>();
        
        for (ColumnMetadata column : columns) {
            String columnName = column.getName();
            ColumnType type = inferColumnType(column, rows, columnName);
            types.put(columnName, type);
        }
        
        return types;
    }

    private ColumnType inferColumnType(ColumnMetadata column, List<Map<String, Object>> rows, String columnName) {
        // 1. 基于数据库类型推断
        String dbType = column.getType().toLowerCase();
        if (dbType.contains("int") || dbType.contains("decimal") || dbType.contains("float")) {
            return isIdColumn(columnName) ? ColumnType.ID : ColumnType.NUMERIC;
        }
        
        if (dbType.contains("date") || dbType.contains("time")) {
            return ColumnType.DATETIME;
        }
        
        // 2. 基于数据内容推断
        if (rows.size() > 0) {
            Object sampleValue = rows.get(0).get(columnName);
            if (sampleValue instanceof Number) {
                return isIdColumn(columnName) ? ColumnType.ID : ColumnType.NUMERIC;
            }
            
            if (sampleValue instanceof Date || isDateString(sampleValue)) {
                return ColumnType.DATETIME;
            }
        }
        
        // 3. 分析数据唯一性
        Set<Object> uniqueValues = rows.stream()
            .map(row -> row.get(columnName))
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());
        
        double uniqueRatio = (double) uniqueValues.size() / rows.size();
        
        if (uniqueRatio > 0.9) {
            return ColumnType.ID;
        } else if (uniqueValues.size() <= 20) {
            return ColumnType.CATEGORICAL;
        }
        
        return ColumnType.TEXT;
    }
}
```

### 图表推荐器
```java
@Service
public class ChartRecommender {

    public List<ChartRecommendation> recommendCharts(DataAnalysisResult analysisResult) {
        List<ChartRecommendation> recommendations = new ArrayList<>();
        
        Map<String, ColumnType> columnTypes = analysisResult.getColumnTypes();
        int numericColumns = (int) columnTypes.values().stream().filter(type -> type == ColumnType.NUMERIC).count();
        int categoricalColumns = (int) columnTypes.values().stream().filter(type -> type == ColumnType.CATEGORICAL).count();
        int dateColumns = (int) columnTypes.values().stream().filter(type -> type == ColumnType.DATETIME).count();
        
        // 1. 时间序列图表
        if (dateColumns > 0 && numericColumns > 0) {
            recommendations.add(ChartRecommendation.builder()
                .chartType(ChartType.LINE)
                .confidence(0.9)
                .reason("检测到时间序列数据，适合使用折线图展示趋势")
                .config(buildTimeSeriesConfig(analysisResult))
                .build());
        }
        
        // 2. 分类对比图表
        if (categoricalColumns == 1 && numericColumns == 1) {
            recommendations.add(ChartRecommendation.builder()
                .chartType(ChartType.BAR)
                .confidence(0.85)
                .reason("一个分类字段和一个数值字段，适合使用柱状图对比")
                .config(buildBarConfig(analysisResult))
                .build());
                
            if (analysisResult.getRowCount() <= 10) {
                recommendations.add(ChartRecommendation.builder()
                    .chartType(ChartType.PIE)
                    .confidence(0.8)
                    .reason("分类数量较少，可以使用饼图展示占比")
                    .config(buildPieConfig(analysisResult))
                    .build());
            }
        }
        
        // 3. 散点图
        if (numericColumns >= 2) {
            recommendations.add(ChartRecommendation.builder()
                .chartType(ChartType.SCATTER)
                .confidence(0.75)
                .reason("多个数值字段，可以使用散点图分析相关性")
                .config(buildScatterConfig(analysisResult))
                .build());
        }
        
        // 4. 表格展示
        recommendations.add(ChartRecommendation.builder()
            .chartType(ChartType.TABLE)
            .confidence(0.6)
            .reason("表格可以展示所有数据的详细信息")
            .config(buildTableConfig(analysisResult))
            .build());
        
        // 按置信度排序
        recommendations.sort((a, b) -> Double.compare(b.getConfidence(), a.getConfidence()));
        
        return recommendations.stream().limit(5).collect(Collectors.toList());
    }

    private ChartConfig buildTimeSeriesConfig(DataAnalysisResult analysisResult) {
        Map<String, ColumnType> columnTypes = analysisResult.getColumnTypes();
        
        String dateColumn = columnTypes.entrySet().stream()
            .filter(entry -> entry.getValue() == ColumnType.DATETIME)
            .map(Map.Entry::getKey)
            .findFirst()
            .orElse(null);
            
        String valueColumn = columnTypes.entrySet().stream()
            .filter(entry -> entry.getValue() == ColumnType.NUMERIC)
            .map(Map.Entry::getKey)
            .findFirst()
            .orElse(null);
        
        return ChartConfig.builder()
            .xAxis(dateColumn)
            .yAxis(valueColumn)
            .chartType(ChartType.LINE)
            .title("时间趋势图")
            .build();
    }

    private ChartConfig buildBarConfig(DataAnalysisResult analysisResult) {
        Map<String, ColumnType> columnTypes = analysisResult.getColumnTypes();
        
        String categoryColumn = columnTypes.entrySet().stream()
            .filter(entry -> entry.getValue() == ColumnType.CATEGORICAL)
            .map(Map.Entry::getKey)
            .findFirst()
            .orElse(null);
            
        String valueColumn = columnTypes.entrySet().stream()
            .filter(entry -> entry.getValue() == ColumnType.NUMERIC)
            .map(Map.Entry::getKey)
            .findFirst()
            .orElse(null);
        
        return ChartConfig.builder()
            .xAxis(categoryColumn)
            .yAxis(valueColumn)
            .chartType(ChartType.BAR)
            .title("分类对比图")
            .build();
    }
}
```

### 图表生成器
```java
@Service
public class ChartGenerator {

    public ChartDefinition generateChart(QueryExecutionResult queryResult, ChartConfig config) {
        try {
            switch (config.getChartType()) {
                case LINE:
                    return generateLineChart(queryResult, config);
                case BAR:
                    return generateBarChart(queryResult, config);
                case PIE:
                    return generatePieChart(queryResult, config);
                case SCATTER:
                    return generateScatterChart(queryResult, config);
                case TABLE:
                    return generateTableChart(queryResult, config);
                default:
                    throw new IllegalArgumentException("不支持的图表类型: " + config.getChartType());
            }
        } catch (Exception e) {
            log.error("图表生成失败", e);
            throw new ChartGenerationException("图表生成失败", e);
        }
    }

    private ChartDefinition generateLineChart(QueryExecutionResult queryResult, ChartConfig config) {
        List<Map<String, Object>> rows = queryResult.getRows();
        String xField = config.getXAxis();
        String yField = config.getYAxis();
        
        // 准备数据
        List<Object> xData = new ArrayList<>();
        List<Object> yData = new ArrayList<>();
        
        for (Map<String, Object> row : rows) {
            xData.add(row.get(xField));
            yData.add(row.get(yField));
        }
        
        // 构建ECharts配置
        Map<String, Object> option = new HashMap<>();
        option.put("title", Map.of("text", config.getTitle()));
        option.put("tooltip", Map.of("trigger", "axis"));
        option.put("xAxis", Map.of(
            "type", "category",
            "data", xData,
            "name", xField
        ));
        option.put("yAxis", Map.of(
            "type", "value",
            "name", yField
        ));
        option.put("series", List.of(Map.of(
            "name", yField,
            "type", "line",
            "data", yData,
            "smooth", true
        )));
        
        return ChartDefinition.builder()
            .chartType(ChartType.LINE)
            .library("echarts")
            .option(option)
            .width(800)
            .height(400)
            .build();
    }

    private ChartDefinition generateBarChart(QueryExecutionResult queryResult, ChartConfig config) {
        List<Map<String, Object>> rows = queryResult.getRows();
        String xField = config.getXAxis();
        String yField = config.getYAxis();
        
        // 聚合数据（如果需要）
        Map<Object, Double> aggregatedData = rows.stream()
            .collect(Collectors.groupingBy(
                row -> row.get(xField),
                Collectors.summingDouble(row -> {
                    Object value = row.get(yField);
                    return value instanceof Number ? ((Number) value).doubleValue() : 0.0;
                })
            ));
        
        List<Object> categories = new ArrayList<>(aggregatedData.keySet());
        List<Object> values = categories.stream()
            .map(aggregatedData::get)
            .collect(Collectors.toList());
        
        Map<String, Object> option = new HashMap<>();
        option.put("title", Map.of("text", config.getTitle()));
        option.put("tooltip", Map.of("trigger", "axis"));
        option.put("xAxis", Map.of(
            "type", "category",
            "data", categories,
            "name", xField
        ));
        option.put("yAxis", Map.of(
            "type", "value",
            "name", yField
        ));
        option.put("series", List.of(Map.of(
            "name", yField,
            "type", "bar",
            "data", values
        )));
        
        return ChartDefinition.builder()
            .chartType(ChartType.BAR)
            .library("echarts")
            .option(option)
            .width(800)
            .height(400)
            .build();
    }
}
```

## 🎨 前端可视化组件

### 图表展示组件
```vue
<template>
  <div class="chart-container">
    <!-- 图表推荐 -->
    <div class="chart-recommendations" v-if="recommendations.length > 0">
      <h4>推荐图表</h4>
      <div class="recommendation-list">
        <el-button
          v-for="rec in recommendations"
          :key="rec.chartType"
          @click="selectChart(rec)"
          :type="selectedChart?.chartType === rec.chartType ? 'primary' : 'default'"
          size="small"
        >
          {{ getChartTypeName(rec.chartType) }}
          <el-tag size="small" style="margin-left: 8px;">
            {{ (rec.confidence * 100).toFixed(0) }}%
          </el-tag>
        </el-button>
      </div>
    </div>
    
    <!-- 图表配置 -->
    <div class="chart-config" v-if="selectedChart">
      <el-form :model="chartConfig" label-width="80px" size="small">
        <el-form-item label="标题">
          <el-input v-model="chartConfig.title" />
        </el-form-item>
        <el-form-item label="X轴" v-if="needsXAxis">
          <el-select v-model="chartConfig.xAxis">
            <el-option
              v-for="column in availableColumns"
              :key="column.name"
              :label="column.name"
              :value="column.name"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="Y轴" v-if="needsYAxis">
          <el-select v-model="chartConfig.yAxis">
            <el-option
              v-for="column in numericColumns"
              :key="column.name"
              :label="column.name"
              :value="column.name"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    
    <!-- 图表展示 -->
    <div class="chart-display">
      <div ref="chartContainer" class="chart-canvas"></div>
    </div>
    
    <!-- 操作按钮 -->
    <div class="chart-actions">
      <el-button @click="refreshChart" size="small">
        <el-icon><Refresh /></el-icon>
        刷新
      </el-button>
      <el-dropdown @command="exportChart">
        <el-button size="small">
          <el-icon><Download /></el-icon>
          导出
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="png">PNG图片</el-dropdown-item>
            <el-dropdown-item command="svg">SVG矢量图</el-dropdown-item>
            <el-dropdown-item command="pdf">PDF文档</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, watch } from 'vue'
import * as echarts from 'echarts'

export default {
  name: 'ChartContainer',
  props: {
    queryResult: {
      type: Object,
      required: true
    }
  },
  setup(props) {
    const chartContainer = ref()
    const chartInstance = ref(null)
    const recommendations = ref([])
    const selectedChart = ref(null)
    const chartConfig = reactive({
      title: '',
      xAxis: '',
      yAxis: ''
    })
    
    const loadRecommendations = async () => {
      try {
        const response = await api.post('/visualization/recommend', {
          queryResult: props.queryResult
        })
        recommendations.value = response.data
        
        if (recommendations.value.length > 0) {
          selectChart(recommendations.value[0])
        }
      } catch (error) {
        ElMessage.error('获取图表推荐失败')
      }
    }
    
    const selectChart = (recommendation) => {
      selectedChart.value = recommendation
      Object.assign(chartConfig, recommendation.config)
      generateChart()
    }
    
    const generateChart = async () => {
      try {
        const response = await api.post('/visualization/generate', {
          queryResult: props.queryResult,
          config: chartConfig
        })
        
        const chartDefinition = response.data
        renderChart(chartDefinition)
        
      } catch (error) {
        ElMessage.error('图表生成失败')
      }
    }
    
    const renderChart = (chartDefinition) => {
      if (chartInstance.value) {
        chartInstance.value.dispose()
      }
      
      chartInstance.value = echarts.init(chartContainer.value)
      chartInstance.value.setOption(chartDefinition.option)
      
      // 响应式调整
      window.addEventListener('resize', () => {
        chartInstance.value?.resize()
      })
    }
    
    onMounted(() => {
      loadRecommendations()
    })
    
    return {
      chartContainer,
      recommendations,
      selectedChart,
      chartConfig,
      selectChart,
      generateChart
    }
  }
}
</script>
```

## 🧪 测试用例

### 功能测试
- 图表推荐算法测试
- 各种图表类型生成测试
- 交互功能测试
- 导出功能测试
- 响应式布局测试

### 性能测试
- 大数据量渲染测试
- 图表切换性能测试
- 内存使用优化测试
- 渲染速度测试

### 兼容性测试
- 不同浏览器兼容性测试
- 移动端适配测试
- 图表库版本兼容测试
- 导出格式兼容测试

---

**相关文档**:
- [AI查询引擎](./ai-query-engine.md)
- [MCP数据库服务](./mcp-database-service.md)
- [训练数据管理](./training-data-management.md)
