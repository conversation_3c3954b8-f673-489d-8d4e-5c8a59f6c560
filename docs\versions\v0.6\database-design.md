# AI-FDB v0.6 - 数据库设计

## 概述

v0.6版本数据库设计在继承v0.1-v0.5版本基础上，新增了AI查询、训练数据管理、可视化配置等相关表结构。本文档详细描述了v0.6版本新增的数据库表设计和关系。

## 🎯 设计原则

- **继承性** - 完全兼容v0.1-v0.5版本的数据库结构
- **扩展性** - 支持AI查询和可视化功能的扩展
- **性能优化** - 针对AI查询场景的索引优化
- **数据完整性** - 完善的约束和外键关系
- **可维护性** - 清晰的命名规范和文档说明

## 📊 新增表结构

### 1. AI查询记录表 (ai_queries)
```sql
CREATE TABLE ai_queries (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    workspace_id BIGINT NOT NULL COMMENT '工作空间ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    original_question TEXT NOT NULL COMMENT '原始问题',
    processed_question TEXT COMMENT '处理后的问题',
    generated_sql TEXT COMMENT '生成的SQL',
    execution_result JSON COMMENT '执行结果',
    confidence_score DECIMAL(3,2) COMMENT 'AI置信度评分',
    execution_time INT COMMENT '执行时间（毫秒）',
    used_tables JSON COMMENT '使用的表列表',
    ai_model VARCHAR(100) COMMENT '使用的AI模型',
    status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending',
    error_message TEXT COMMENT '错误信息',
    feedback_rating INT COMMENT '用户反馈评分 1-5',
    feedback_comments TEXT COMMENT '用户反馈意见',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (workspace_id) REFERENCES workspaces(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_workspace_user (workspace_id, user_id),
    INDEX idx_status (status),
    INDEX idx_confidence_score (confidence_score),
    INDEX idx_created_at (created_at),
    FULLTEXT INDEX ft_original_question (original_question)
) COMMENT='AI查询记录表';
```

### 2. 训练数据表 (training_data)
```sql
CREATE TABLE training_data (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    workspace_id BIGINT NOT NULL COMMENT '工作空间ID',
    question TEXT NOT NULL COMMENT '训练问题',
    sql_query TEXT NOT NULL COMMENT '对应的SQL查询',
    table_schemas JSON COMMENT '相关表结构信息',
    execution_result JSON COMMENT '执行结果',
    confidence_score DECIMAL(3,2) COMMENT '置信度评分',
    quality_score DECIMAL(3,2) COMMENT '质量评分',
    user_feedback JSON COMMENT '用户反馈',
    vector_id VARCHAR(255) COMMENT '向量数据库ID',
    data_source ENUM('user_query', 'manual_input', 'import') DEFAULT 'user_query',
    is_validated BOOLEAN DEFAULT FALSE COMMENT '是否已验证',
    validated_by BIGINT COMMENT '验证人员ID',
    validated_at TIMESTAMP NULL COMMENT '验证时间',
    created_by BIGINT NOT NULL COMMENT '创建者ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (workspace_id) REFERENCES workspaces(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (validated_by) REFERENCES users(id),
    INDEX idx_workspace_id (workspace_id),
    INDEX idx_quality_score (quality_score),
    INDEX idx_is_validated (is_validated),
    INDEX idx_data_source (data_source),
    FULLTEXT INDEX ft_question (question)
) COMMENT='训练数据表';
```

### 3. 查询会话表 (query_sessions)
```sql
CREATE TABLE query_sessions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    workspace_id BIGINT NOT NULL COMMENT '工作空间ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    session_name VARCHAR(255) COMMENT '会话名称',
    context_data JSON COMMENT '会话上下文数据',
    query_count INT DEFAULT 0 COMMENT '查询次数',
    last_query_at TIMESTAMP NULL COMMENT '最后查询时间',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否活跃',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (workspace_id) REFERENCES workspaces(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_workspace_user (workspace_id, user_id),
    INDEX idx_is_active (is_active),
    INDEX idx_last_query_at (last_query_at)
) COMMENT='查询会话表';
```

### 4. 可视化配置表 (visualization_configs)
```sql
CREATE TABLE visualization_configs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    workspace_id BIGINT NOT NULL COMMENT '工作空间ID',
    query_id BIGINT COMMENT '关联查询ID',
    chart_type ENUM('line', 'bar', 'pie', 'scatter', 'table', 'heatmap') NOT NULL,
    chart_config JSON NOT NULL COMMENT '图表配置',
    chart_title VARCHAR(255) COMMENT '图表标题',
    x_axis_field VARCHAR(100) COMMENT 'X轴字段',
    y_axis_field VARCHAR(100) COMMENT 'Y轴字段',
    color_field VARCHAR(100) COMMENT '颜色字段',
    size_field VARCHAR(100) COMMENT '大小字段',
    is_template BOOLEAN DEFAULT FALSE COMMENT '是否为模板',
    template_name VARCHAR(255) COMMENT '模板名称',
    usage_count INT DEFAULT 0 COMMENT '使用次数',
    created_by BIGINT NOT NULL COMMENT '创建者ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (workspace_id) REFERENCES workspaces(id) ON DELETE CASCADE,
    FOREIGN KEY (query_id) REFERENCES ai_queries(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_workspace_id (workspace_id),
    INDEX idx_chart_type (chart_type),
    INDEX idx_is_template (is_template),
    INDEX idx_usage_count (usage_count)
) COMMENT='可视化配置表';
```

### 5. AI模型配置表 (ai_model_configs)
```sql
CREATE TABLE ai_model_configs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    model_name VARCHAR(100) NOT NULL COMMENT '模型名称',
    model_provider ENUM('openai', 'baidu', 'alibaba', 'custom') NOT NULL,
    api_endpoint VARCHAR(500) COMMENT 'API端点',
    api_key_encrypted TEXT COMMENT '加密的API密钥',
    model_parameters JSON COMMENT '模型参数配置',
    prompt_template TEXT COMMENT '提示词模板',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    is_default BOOLEAN DEFAULT FALSE COMMENT '是否为默认模型',
    max_tokens INT DEFAULT 4000 COMMENT '最大Token数',
    temperature DECIMAL(3,2) DEFAULT 0.10 COMMENT '温度参数',
    timeout_seconds INT DEFAULT 30 COMMENT '超时时间（秒）',
    cost_per_1k_tokens DECIMAL(10,6) COMMENT '每1K Token成本',
    created_by BIGINT NOT NULL COMMENT '创建者ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_model_provider (model_provider),
    INDEX idx_is_active (is_active),
    INDEX idx_is_default (is_default),
    UNIQUE KEY uk_model_name (model_name)
) COMMENT='AI模型配置表';
```

### 6. 查询性能统计表 (query_performance_stats)
```sql
CREATE TABLE query_performance_stats (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    workspace_id BIGINT NOT NULL COMMENT '工作空间ID',
    date_key DATE NOT NULL COMMENT '统计日期',
    total_queries INT DEFAULT 0 COMMENT '总查询数',
    successful_queries INT DEFAULT 0 COMMENT '成功查询数',
    failed_queries INT DEFAULT 0 COMMENT '失败查询数',
    avg_execution_time DECIMAL(10,2) COMMENT '平均执行时间（毫秒）',
    avg_confidence_score DECIMAL(3,2) COMMENT '平均置信度',
    avg_user_rating DECIMAL(3,2) COMMENT '平均用户评分',
    unique_users INT DEFAULT 0 COMMENT '活跃用户数',
    most_used_tables JSON COMMENT '最常用表列表',
    common_query_patterns JSON COMMENT '常见查询模式',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (workspace_id) REFERENCES workspaces(id) ON DELETE CASCADE,
    INDEX idx_workspace_date (workspace_id, date_key),
    INDEX idx_date_key (date_key),
    UNIQUE KEY uk_workspace_date (workspace_id, date_key)
) COMMENT='查询性能统计表';
```

### 7. 数据库连接配置表 (database_connections)
```sql
CREATE TABLE database_connections (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    workspace_id BIGINT NOT NULL COMMENT '工作空间ID',
    connection_name VARCHAR(255) NOT NULL COMMENT '连接名称',
    database_type ENUM('mysql', 'postgresql', 'sqlserver', 'oracle', 'sqlite') NOT NULL,
    host VARCHAR(255) COMMENT '主机地址',
    port INT COMMENT '端口号',
    database_name VARCHAR(255) COMMENT '数据库名',
    username VARCHAR(255) COMMENT '用户名',
    password_encrypted TEXT COMMENT '加密的密码',
    connection_params JSON COMMENT '连接参数',
    is_readonly BOOLEAN DEFAULT TRUE COMMENT '是否只读',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    max_connections INT DEFAULT 10 COMMENT '最大连接数',
    connection_timeout INT DEFAULT 30 COMMENT '连接超时（秒）',
    query_timeout INT DEFAULT 60 COMMENT '查询超时（秒）',
    last_test_at TIMESTAMP NULL COMMENT '最后测试时间',
    test_status ENUM('success', 'failed', 'pending') COMMENT '测试状态',
    created_by BIGINT NOT NULL COMMENT '创建者ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (workspace_id) REFERENCES workspaces(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_workspace_id (workspace_id),
    INDEX idx_is_active (is_active),
    INDEX idx_database_type (database_type),
    UNIQUE KEY uk_workspace_name (workspace_id, connection_name)
) COMMENT='数据库连接配置表';
```

## 🔗 表关系图

```
users (用户表)
  ├── ai_queries (AI查询记录)
  ├── training_data (训练数据)
  ├── query_sessions (查询会话)
  ├── visualization_configs (可视化配置)
  ├── ai_model_configs (AI模型配置)
  └── database_connections (数据库连接)

workspaces (工作空间)
  ├── ai_queries (AI查询记录)
  ├── training_data (训练数据)
  ├── query_sessions (查询会话)
  ├── visualization_configs (可视化配置)
  ├── query_performance_stats (查询性能统计)
  └── database_connections (数据库连接)

ai_queries (AI查询记录)
  ├── visualization_configs (可视化配置)
  └── query_sessions (查询会话) [通过session_id关联]
```

## 📈 性能优化

### 索引策略
- **复合索引** - workspace_id + user_id 的复合索引
- **时间索引** - 创建时间和更新时间的索引
- **状态索引** - 查询状态和活跃状态索引
- **全文索引** - 问题和SQL的全文搜索索引
- **评分索引** - 置信度和质量评分的索引

### 分区策略
```sql
-- 按月分区AI查询记录表
ALTER TABLE ai_queries 
PARTITION BY RANGE (YEAR(created_at) * 100 + MONTH(created_at)) (
    PARTITION p202401 VALUES LESS THAN (202402),
    PARTITION p202402 VALUES LESS THAN (202403),
    -- ... 更多分区
    PARTITION p_future VALUES LESS THAN MAXVALUE
);

-- 按日期分区性能统计表
ALTER TABLE query_performance_stats
PARTITION BY RANGE (TO_DAYS(date_key)) (
    PARTITION p20240101 VALUES LESS THAN (TO_DAYS('2024-02-01')),
    PARTITION p20240201 VALUES LESS THAN (TO_DAYS('2024-03-01')),
    -- ... 更多分区
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

### 查询优化
- **缓存策略** - 频繁查询的结果缓存
- **分页优化** - 使用游标分页避免深度分页
- **JSON字段优化** - 使用生成列和函数索引
- **统计查询优化** - 预计算统计数据

## 🔒 数据安全

### 敏感数据加密
```sql
-- API密钥加密存储
UPDATE ai_model_configs 
SET api_key_encrypted = AES_ENCRYPT(api_key, 'encryption_key')
WHERE api_key IS NOT NULL;

-- 数据库密码加密存储
UPDATE database_connections 
SET password_encrypted = AES_ENCRYPT(password, 'encryption_key')
WHERE password IS NOT NULL;
```

### 访问控制
- **行级安全** - 基于workspace_id的行级访问控制
- **字段级权限** - 敏感字段的访问权限控制
- **审计日志** - 完整的数据操作审计记录
- **数据脱敏** - 测试环境的敏感数据脱敏

## 🧪 数据迁移

### v0.5到v0.6迁移脚本
```sql
-- 创建新表
SOURCE create_v0.6_tables.sql;

-- 初始化AI模型配置
INSERT INTO ai_model_configs (model_name, model_provider, is_default, created_by)
VALUES ('qwen-turbo', 'alibaba', TRUE, 1);

-- 创建默认数据库连接
INSERT INTO database_connections (workspace_id, connection_name, database_type, 
    host, port, database_name, is_readonly, created_by)
SELECT id, 'Default MySQL', 'mysql', 'localhost', 3306, 'ai_fdb', TRUE, 1
FROM workspaces;

-- 更新索引
ANALYZE TABLE ai_queries, training_data, visualization_configs;
```

### 数据清理脚本
```sql
-- 清理过期的查询记录（保留3个月）
DELETE FROM ai_queries 
WHERE created_at < DATE_SUB(NOW(), INTERVAL 3 MONTH)
AND feedback_rating IS NULL;

-- 清理低质量训练数据
DELETE FROM training_data 
WHERE quality_score < 0.3 
AND created_at < DATE_SUB(NOW(), INTERVAL 1 MONTH);
```

---

**相关文档**:
- [v0.1数据库设计](../v0.1/database-design.md)
- [v0.2数据库设计](../v0.2/database-design.md)
- [v0.3数据库设计](../v0.3/database-design.md)
- [v0.4数据管理实现](../v0.4/data-management-implementation.md)
- [v0.5数据库设计](../v0.5/database-design.md)
