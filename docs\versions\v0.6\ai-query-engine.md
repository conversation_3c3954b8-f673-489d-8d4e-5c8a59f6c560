# AI-FDB v0.6 - AI查询引擎

## 概述

AI查询引擎是v0.6版本的核心组件，集成Vanna AI框架和通义千问模型，实现自然语言到SQL的智能转换。用户可以使用自然语言提问，系统自动理解意图、识别相关表和字段，生成准确的SQL查询语句。

## 🎯 功能目标

- **自然语言理解** - 准确理解用户的查询意图和需求
- **智能表识别** - 自动识别相关的数据表和字段
- **SQL自动生成** - 基于问题和表结构生成准确的SQL语句
- **上下文对话** - 支持多轮对话和上下文理解
- **查询优化** - 提供SQL优化建议和性能提示
- **安全控制** - 防止SQL注入和危险操作

## 🏗️ 系统架构

### AI查询流程
```
自然语言输入 → 意图理解 → 表结构匹配 → SQL生成 → 安全验证 → 查询执行 → 结果返回
      ↓           ↓         ↓         ↓        ↓        ↓        ↓
   文本预处理   意图识别   向量检索   模板生成  安全检查  MCP执行  结果格式化
```

### 核心组件
1. **NLUProcessor** - 自然语言理解处理器
2. **IntentRecognizer** - 意图识别器
3. **SchemaRetriever** - 表结构检索器
4. **SQLGenerator** - SQL生成器
5. **SecurityValidator** - 安全验证器
6. **ContextManager** - 上下文管理器

## 🔧 技术实现

### Vanna AI配置
```yaml
# application.yml
ai:
  vanna:
    # 模型配置
    model: qwen-turbo
    api-key: ${QWEN_API_KEY:sk-beff2b8bc208457a9d971610488661f0}
    base-url: https://dashscope.aliyuncs.com
    temperature: 0.1
    max-tokens: 4000
    
    # 向量数据库配置
    vector-store:
      type: chromadb
      path: ./data/vanna_vectordb
      collection-name: ai-fdb-training
      
    # 查询配置
    query:
      max-results: 1000
      timeout: 30000
      enable-cache: true
      cache-ttl: 3600
      
    # 安全配置
    security:
      allowed-operations: ["SELECT", "SHOW", "DESCRIBE", "EXPLAIN"]
      forbidden-keywords: ["DROP", "DELETE", "UPDATE", "INSERT", "ALTER", "CREATE", "TRUNCATE"]
      max-query-complexity: 10
```

### AI查询服务实现
```java
@Service
public class AIQueryService {

    @Autowired
    private VannaAIClient vannaClient;
    
    @Autowired
    private SchemaRetriever schemaRetriever;
    
    @Autowired
    private SecurityValidator securityValidator;
    
    @Autowired
    private ContextManager contextManager;

    public QueryResult processNaturalLanguageQuery(NLQueryRequest request) {
        try {
            // 1. 预处理用户输入
            String processedQuery = preprocessQuery(request.getQuestion());
            
            // 2. 获取上下文信息
            QueryContext context = contextManager.getContext(request.getUserId(), request.getWorkspaceId());
            
            // 3. 检索相关表结构
            List<TableSchema> relevantSchemas = schemaRetriever.retrieveRelevantSchemas(
                processedQuery, request.getWorkspaceId());
            
            // 4. 生成SQL语句
            SQLGenerationResult sqlResult = vannaClient.generateSQL(
                processedQuery, relevantSchemas, context);
            
            // 5. 安全验证
            SecurityValidationResult validation = securityValidator.validate(sqlResult.getSql());
            if (!validation.isValid()) {
                throw new SecurityException("查询包含不安全的操作: " + validation.getErrors());
            }
            
            // 6. 构建查询结果
            return QueryResult.builder()
                .originalQuestion(request.getQuestion())
                .generatedSQL(sqlResult.getSql())
                .confidence(sqlResult.getConfidence())
                .explanation(sqlResult.getExplanation())
                .usedTables(relevantSchemas.stream().map(TableSchema::getName).collect(Collectors.toList()))
                .build();
                
        } catch (Exception e) {
            log.error("AI查询处理失败", e);
            throw new AIQueryException("查询处理失败: " + e.getMessage());
        }
    }

    private String preprocessQuery(String query) {
        // 1. 文本清理
        String cleaned = query.trim().toLowerCase();
        
        // 2. 同义词替换
        cleaned = applySynonymReplacement(cleaned);
        
        // 3. 业务术语标准化
        cleaned = normalizeBusinessTerms(cleaned);
        
        return cleaned;
    }
}
```

### Vanna AI客户端
```java
@Component
public class VannaAIClient {

    @Value("${ai.vanna.api-key}")
    private String apiKey;
    
    @Value("${ai.vanna.base-url}")
    private String baseUrl;
    
    @Autowired
    private RestTemplate restTemplate;
    
    @Autowired
    private VectorStoreService vectorStoreService;

    public SQLGenerationResult generateSQL(String question, List<TableSchema> schemas, QueryContext context) {
        try {
            // 1. 构建提示词
            String prompt = buildSQLPrompt(question, schemas, context);
            
            // 2. 调用通义千问API
            QwenResponse response = callQwenAPI(prompt);
            
            // 3. 解析SQL结果
            String generatedSQL = extractSQLFromResponse(response.getContent());
            
            // 4. 计算置信度
            double confidence = calculateConfidence(question, generatedSQL, schemas);
            
            // 5. 生成解释
            String explanation = generateExplanation(question, generatedSQL, schemas);
            
            return SQLGenerationResult.builder()
                .sql(generatedSQL)
                .confidence(confidence)
                .explanation(explanation)
                .model("qwen-turbo")
                .build();
                
        } catch (Exception e) {
            log.error("SQL生成失败", e);
            throw new SQLGenerationException("SQL生成失败", e);
        }
    }

    private String buildSQLPrompt(String question, List<TableSchema> schemas, QueryContext context) {
        StringBuilder prompt = new StringBuilder();
        
        prompt.append("你是一个专业的SQL查询专家。请根据用户问题和数据库表结构生成准确的SQL查询语句。\n\n");
        
        // 添加表结构信息
        prompt.append("数据库表结构：\n");
        for (TableSchema schema : schemas) {
            prompt.append("表名: ").append(schema.getName()).append("\n");
            prompt.append("表描述: ").append(schema.getDescription()).append("\n");
            prompt.append("字段信息:\n");
            for (FieldSchema field : schema.getFields()) {
                prompt.append("  - ").append(field.getName())
                      .append(" (").append(field.getType()).append(")")
                      .append(": ").append(field.getDescription()).append("\n");
            }
            prompt.append("\n");
        }
        
        // 添加上下文信息
        if (context.hasHistory()) {
            prompt.append("对话历史:\n");
            for (QueryHistory history : context.getHistory()) {
                prompt.append("Q: ").append(history.getQuestion()).append("\n");
                prompt.append("SQL: ").append(history.getSql()).append("\n\n");
            }
        }
        
        // 添加用户问题
        prompt.append("用户问题: ").append(question).append("\n\n");
        
        // 添加生成要求
        prompt.append("请生成SQL查询语句，要求：\n");
        prompt.append("1. 只返回SQL语句，不要包含其他解释\n");
        prompt.append("2. 使用标准SQL语法\n");
        prompt.append("3. 确保查询逻辑正确\n");
        prompt.append("4. 只使用SELECT语句\n");
        prompt.append("5. 适当使用LIMIT限制结果数量\n\n");
        
        prompt.append("SQL查询语句：\n");
        
        return prompt.toString();
    }

    private QwenResponse callQwenAPI(String prompt) {
        QwenRequest request = QwenRequest.builder()
            .model("qwen-turbo")
            .messages(List.of(
                QwenMessage.builder()
                    .role("user")
                    .content(prompt)
                    .build()
            ))
            .temperature(0.1)
            .maxTokens(4000)
            .build();
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth(apiKey);
        
        HttpEntity<QwenRequest> entity = new HttpEntity<>(request, headers);
        
        ResponseEntity<QwenResponse> response = restTemplate.postForEntity(
            baseUrl + "/chat/completions", entity, QwenResponse.class);
        
        if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
            return response.getBody();
        } else {
            throw new AIServiceException("通义千问API调用失败: " + response.getStatusCode());
        }
    }
}
```

### 表结构检索器
```java
@Service
public class SchemaRetriever {

    @Autowired
    private VectorStoreService vectorStoreService;
    
    @Autowired
    private DataTableRepository tableRepository;

    public List<TableSchema> retrieveRelevantSchemas(String question, Long workspaceId) {
        // 1. 向量检索相关表
        List<String> relevantTableNames = vectorStoreService.searchRelevantTables(question, workspaceId);
        
        // 2. 获取表结构详情
        List<TableSchema> schemas = new ArrayList<>();
        for (String tableName : relevantTableNames) {
            DataTable table = tableRepository.findByNameAndWorkspaceId(tableName, workspaceId);
            if (table != null) {
                TableSchema schema = convertToSchema(table);
                schemas.add(schema);
            }
        }
        
        // 3. 如果没有找到相关表，返回所有表
        if (schemas.isEmpty()) {
            List<DataTable> allTables = tableRepository.findByWorkspaceId(workspaceId);
            schemas = allTables.stream()
                .map(this::convertToSchema)
                .collect(Collectors.toList());
        }
        
        return schemas;
    }

    private TableSchema convertToSchema(DataTable table) {
        List<FieldSchema> fields = table.getFields().stream()
            .map(field -> FieldSchema.builder()
                .name(field.getFieldName())
                .type(field.getFieldType())
                .description(field.getDescription())
                .required(field.getRequired())
                .build())
            .collect(Collectors.toList());
        
        return TableSchema.builder()
            .name(table.getName())
            .displayName(table.getDisplayName())
            .description(table.getDescription())
            .fields(fields)
            .build();
    }
}
```

### 安全验证器
```java
@Service
public class SecurityValidator {

    private static final List<String> FORBIDDEN_KEYWORDS = Arrays.asList(
        "DROP", "DELETE", "UPDATE", "INSERT", "ALTER", "CREATE", "TRUNCATE", 
        "GRANT", "REVOKE", "EXEC", "EXECUTE", "CALL"
    );
    
    private static final List<String> ALLOWED_OPERATIONS = Arrays.asList(
        "SELECT", "SHOW", "DESCRIBE", "EXPLAIN"
    );

    public SecurityValidationResult validate(String sql) {
        List<String> errors = new ArrayList<>();
        
        // 1. 检查禁用关键词
        String upperSQL = sql.toUpperCase();
        for (String keyword : FORBIDDEN_KEYWORDS) {
            if (upperSQL.contains(keyword)) {
                errors.add("包含禁用的关键词: " + keyword);
            }
        }
        
        // 2. 检查允许的操作
        boolean hasAllowedOperation = false;
        for (String operation : ALLOWED_OPERATIONS) {
            if (upperSQL.startsWith(operation)) {
                hasAllowedOperation = true;
                break;
            }
        }
        
        if (!hasAllowedOperation) {
            errors.add("只允许查询操作，不允许修改数据");
        }
        
        // 3. 检查SQL注入模式
        if (containsSQLInjectionPatterns(sql)) {
            errors.add("检测到潜在的SQL注入攻击");
        }
        
        // 4. 检查查询复杂度
        int complexity = calculateQueryComplexity(sql);
        if (complexity > 10) {
            errors.add("查询复杂度过高，可能影响性能");
        }
        
        return SecurityValidationResult.builder()
            .valid(errors.isEmpty())
            .errors(errors)
            .complexity(complexity)
            .build();
    }

    private boolean containsSQLInjectionPatterns(String sql) {
        String[] injectionPatterns = {
            "';", "--", "/*", "*/", "xp_", "sp_", "union", "or 1=1", "and 1=1"
        };
        
        String lowerSQL = sql.toLowerCase();
        for (String pattern : injectionPatterns) {
            if (lowerSQL.contains(pattern)) {
                return true;
            }
        }
        
        return false;
    }

    private int calculateQueryComplexity(String sql) {
        int complexity = 0;
        String upperSQL = sql.toUpperCase();
        
        // 基础复杂度
        complexity += 1;
        
        // JOIN操作增加复杂度
        complexity += countOccurrences(upperSQL, "JOIN") * 2;
        
        // 子查询增加复杂度
        complexity += countOccurrences(upperSQL, "SELECT") - 1;
        
        // 聚合函数增加复杂度
        complexity += countOccurrences(upperSQL, "GROUP BY");
        complexity += countOccurrences(upperSQL, "ORDER BY");
        complexity += countOccurrences(upperSQL, "HAVING");
        
        return complexity;
    }
}
```

## 🎯 查询模式

### 基础查询模式
- **简单查询** - "显示所有用户"
- **条件查询** - "显示状态为活跃的用户"
- **排序查询** - "按注册时间排序显示用户"
- **限制查询** - "显示最新的10个用户"

### 聚合查询模式
- **计数查询** - "统计用户总数"
- **分组统计** - "按部门统计员工数量"
- **时间聚合** - "统计每月的注册用户数"
- **多维分析** - "按地区和年龄段统计用户分布"

### 关联查询模式
- **内连接** - "显示用户及其订单信息"
- **左连接** - "显示所有用户，包括没有订单的用户"
- **多表关联** - "显示用户、订单和产品的关联信息"

## 🧪 测试用例

### 功能测试
- 自然语言理解准确性测试
- SQL生成正确性测试
- 表结构匹配测试
- 上下文对话测试
- 安全验证测试

### 性能测试
- 查询响应时间测试
- 并发查询处理测试
- 向量检索性能测试
- 缓存机制效果测试

### 准确性测试
- SQL生成准确率统计
- 复杂查询处理测试
- 边界情况处理测试
- 错误恢复测试

---

**相关文档**:
- [MCP数据库服务](./mcp-database-service.md)
- [数据可视化系统](./data-visualization-system.md)
- [训练数据管理](./training-data-management.md)
