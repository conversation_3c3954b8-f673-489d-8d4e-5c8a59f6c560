# AI-FDB - AI驱动的表单数据库系统

## 📖 项目简介

AI-FDB（AI Form Database）是一个智能化的表单数据库管理系统，集成了OCR文档识别、AI语义抽取、自然语言查询等先进技术。系统支持多种文档格式的智能处理，能够将非结构化数据转换为结构化数据，并提供强大的数据管理、分析和可视化功能。

## 🎯 核心特性

- **🔐 用户认证系统** - 完整的用户注册、登录、权限管理
- **🤖 AI核心模块** - 集成PaddleOCR和通义千问，支持文档识别和语义抽取
- **🏢 工作空间管理** - 多工作空间支持，团队协作功能
- **📊 数据管理** - 强大的数据导入、展示、编辑功能
- **🔧 高级数据处理** - 数据清洗、转换、分析和质量监控
- **💬 AI查询** - 自然语言数据库查询和智能可视化

## 🛠️ 技术栈

### 后端技术
- **Java 17** + **Spring Boot 3.2** - 主要后端框架
- **Spring Security** + **JWT** - 安全认证
- **MySQL 8.0** + **Redis** - 数据存储
- **PaddleOCR** - OCR文档识别
- **通义千问** - AI语义理解

### 前端技术
- **Vue 3** + **TypeScript** - 前端框架
- **Element Plus** - UI组件库
- **Vite** - 构建工具
- **ECharts** - 数据可视化

### AI与数据处理
- **Vanna AI** - 自然语言到SQL转换
- **Apache Spark** - 大数据处理
- **ChromaDB** - 向量数据库
- **Bytebase DBHub** - MCP数据库服务

## 📋 版本规划

### 🚀 已发布版本

| 版本 | 名称 | 核心功能 | 状态 |
|------|------|----------|------|
| [v0.1](docs/versions/v0.1/README.md) | 项目初始化与用户鉴权 | 用户认证、权限管理、基础架构 | ✅ 已完成 |
| [v0.2](docs/versions/v0.2/README.md) | AI核心模块 | PaddleOCR集成、通义千问集成、文档识别 | ✅ 已完成 |
| [v0.3](docs/versions/v0.3/README.md) | 工作空间管理 | 工作空间、数据表设计器、AI辅助设计 | ✅ 已完成 |

### 🔄 开发中版本

| 版本 | 名称 | 核心功能 | 状态 |
|------|------|----------|------|
| [v0.4](docs/versions/v0.4/README.md) | 数据导入与管理 | 数据导入、表格展示、数据编辑 | 🚧 开发中 |
| [v0.5](docs/versions/v0.5/README.md) | 高级数据处理与分析 | 数据清洗、转换、分析、质量监控 | 📋 计划中 |
| [v0.6](docs/versions/v0.6/README.md) | AI数据库查询与可视化 | 自然语言查询、智能可视化 | 📋 计划中 |

## 🚀 快速开始

### 环境要求
- **Java 17+**
- **Node.js 18+**
- **MySQL 8.0+**
- **Redis 6.0+**
- **Python 3.8+** (用于AI服务)

### 安装步骤

1. **克隆项目**
```bash
git clone https://gitgoooooooooo.inayun.net/zwb/AI-FDB.git
cd AI-FDB
```

2. **后端启动**
```bash
cd backend
mvn clean install
mvn spring-boot:run
```

3. **前端启动**
```bash
cd frontend
npm install
npm run dev
```

4. **AI服务启动**
```bash
cd ai-service
pip install -r requirements.txt
python app.py
```

### 访问地址
- **前端应用**: http://localhost:3000
- **后端API**: http://localhost:8080
- **API文档**: http://localhost:8080/swagger-ui.html

## 📚 文档目录

### 📖 核心文档
- [项目架构设计](docs/architecture/README.md) - 系统整体架构设计
- [数据库设计](docs/database/README.md) - 数据库表结构设计
- [API接口文档](docs/api/README.md) - 后端API接口规范
- [部署指南](docs/deployment/README.md) - 系统部署和运维

### 📋 版本文档
- [v0.1 文档](docs/versions/v0.1/) - 项目初始化与用户鉴权
- [v0.2 文档](docs/versions/v0.2/) - AI核心模块
- [v0.3 文档](docs/versions/v0.3/) - 工作空间管理
- [v0.4 文档](docs/versions/v0.4/) - 数据导入与管理
- [v0.5 文档](docs/versions/v0.5/) - 高级数据处理与分析
- [v0.6 文档](docs/versions/v0.6/) - AI数据库查询与可视化

### 🔧 开发文档
- [开发环境搭建](docs/development/setup.md) - 开发环境配置
- [代码规范](docs/development/coding-standards.md) - 代码编写规范
- [测试指南](docs/development/testing.md) - 单元测试和集成测试
- [贡献指南](docs/development/contributing.md) - 项目贡献指南

## 🤝 贡献

我们欢迎所有形式的贡献，包括但不限于：
- 🐛 Bug报告
- 💡 功能建议
- 📝 文档改进
- 🔧 代码贡献

请查看 [贡献指南](docs/development/contributing.md) 了解详细信息。

## 📄 许可证

本项目采用 [MIT License](LICENSE) 许可证。

## 📞 联系我们

- **项目地址**: https://gitgoooooooooo.inayun.net/zwb/AI-FDB
- **问题反馈**: 请在GitHub Issues中提交
- **邮箱**: <EMAIL>

---

**注意**: 本项目正在积极开发中，功能和API可能会发生变化。建议在生产环境使用前仔细测试。
