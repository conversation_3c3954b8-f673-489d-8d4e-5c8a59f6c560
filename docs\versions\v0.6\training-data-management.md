# AI-FDB v0.6 - 训练数据管理

## 概述

训练数据管理系统负责收集、存储和管理AI查询引擎的训练数据，包括问题-SQL对、表结构信息、查询反馈等。通过向量数据库存储和检索，实现AI模型的持续学习和优化，提升查询准确性和用户体验。

## 🎯 功能目标

- **训练数据收集** - 自动收集用户查询和反馈数据
- **向量化存储** - 将文本数据转换为向量并存储
- **相似性检索** - 基于向量相似性检索相关训练样本
- **质量评估** - 评估训练数据的质量和有效性
- **持续学习** - 基于新数据持续优化模型性能
- **数据清洗** - 清理和标准化训练数据

## 🏗️ 系统架构

### 训练数据流程
```
用户查询 → 数据收集 → 向量化 → 存储管理 → 相似性检索 → 模型训练 → 性能评估
    ↓        ↓        ↓       ↓        ↓         ↓        ↓
  查询记录  数据清洗  文本编码  向量存储  检索匹配   学习优化  质量监控
```

### 核心组件
1. **DataCollector** - 数据收集器
2. **VectorEncoder** - 向量编码器
3. **VectorStore** - 向量存储器
4. **SimilaritySearcher** - 相似性检索器
5. **QualityEvaluator** - 质量评估器
6. **LearningOptimizer** - 学习优化器

## 🔧 技术实现

### 向量数据库配置
```yaml
# application.yml
training:
  vector-store:
    # ChromaDB配置
    type: chromadb
    host: localhost
    port: 8000
    collection-name: ai-fdb-training
    
    # 向量配置
    embedding:
      model: text-embedding-ada-002
      dimension: 1536
      batch-size: 100
      
    # 检索配置
    search:
      top-k: 10
      similarity-threshold: 0.8
      enable-rerank: true
      
  # 数据收集配置
  collection:
    enable-auto-collection: true
    collect-successful-queries: true
    collect-failed-queries: true
    collect-user-feedback: true
    
  # 质量控制
  quality:
    min-confidence-score: 0.7
    enable-data-validation: true
    auto-cleanup-threshold: 0.3
```

### 数据收集器
```java
@Service
public class TrainingDataCollector {

    @Autowired
    private VectorStoreService vectorStoreService;
    
    @Autowired
    private TrainingDataRepository trainingDataRepository;
    
    @Autowired
    private QualityEvaluator qualityEvaluator;

    @EventListener
    public void onQueryExecuted(QueryExecutedEvent event) {
        if (!isCollectionEnabled()) {
            return;
        }
        
        try {
            // 收集查询数据
            TrainingData trainingData = TrainingData.builder()
                .question(event.getOriginalQuestion())
                .sql(event.getGeneratedSQL())
                .tableSchemas(event.getUsedTables())
                .executionResult(event.getExecutionResult())
                .confidence(event.getConfidence())
                .userId(event.getUserId())
                .workspaceId(event.getWorkspaceId())
                .timestamp(LocalDateTime.now())
                .build();
            
            // 质量评估
            QualityScore qualityScore = qualityEvaluator.evaluate(trainingData);
            trainingData.setQualityScore(qualityScore.getScore());
            
            // 保存到数据库
            trainingData = trainingDataRepository.save(trainingData);
            
            // 向量化并存储
            if (qualityScore.getScore() >= getMinConfidenceScore()) {
                vectorizeAndStore(trainingData);
            }
            
        } catch (Exception e) {
            log.error("训练数据收集失败", e);
        }
    }

    @EventListener
    public void onUserFeedback(UserFeedbackEvent event) {
        try {
            // 更新训练数据的反馈信息
            Optional<TrainingData> trainingDataOpt = trainingDataRepository
                .findByQueryId(event.getQueryId());
            
            if (trainingDataOpt.isPresent()) {
                TrainingData trainingData = trainingDataOpt.get();
                
                UserFeedback feedback = UserFeedback.builder()
                    .rating(event.getRating())
                    .correctedSQL(event.getCorrectedSQL())
                    .comments(event.getComments())
                    .feedbackType(event.getFeedbackType())
                    .build();
                
                trainingData.setUserFeedback(feedback);
                
                // 重新评估质量
                QualityScore newQualityScore = qualityEvaluator.evaluate(trainingData);
                trainingData.setQualityScore(newQualityScore.getScore());
                
                trainingDataRepository.save(trainingData);
                
                // 更新向量存储
                updateVectorStore(trainingData);
            }
            
        } catch (Exception e) {
            log.error("用户反馈处理失败", e);
        }
    }

    private void vectorizeAndStore(TrainingData trainingData) {
        try {
            // 构建训练文本
            String trainingText = buildTrainingText(trainingData);
            
            // 生成向量
            float[] embedding = vectorStoreService.generateEmbedding(trainingText);
            
            // 存储到向量数据库
            VectorDocument document = VectorDocument.builder()
                .id(trainingData.getId().toString())
                .text(trainingText)
                .embedding(embedding)
                .metadata(buildMetadata(trainingData))
                .build();
            
            vectorStoreService.addDocument(document);
            
        } catch (Exception e) {
            log.error("向量化存储失败", e);
        }
    }

    private String buildTrainingText(TrainingData trainingData) {
        StringBuilder text = new StringBuilder();
        
        // 添加问题
        text.append("问题: ").append(trainingData.getQuestion()).append("\n");
        
        // 添加表结构信息
        text.append("相关表: ");
        for (String tableName : trainingData.getTableSchemas()) {
            text.append(tableName).append(" ");
        }
        text.append("\n");
        
        // 添加SQL
        text.append("SQL: ").append(trainingData.getSql()).append("\n");
        
        return text.toString();
    }
}
```

### 向量存储服务
```java
@Service
public class VectorStoreService {

    @Value("${training.vector-store.host}")
    private String chromaHost;
    
    @Value("${training.vector-store.port}")
    private int chromaPort;
    
    @Value("${training.vector-store.collection-name}")
    private String collectionName;
    
    @Autowired
    private EmbeddingService embeddingService;
    
    private ChromaClient chromaClient;

    @PostConstruct
    public void initializeChromaClient() {
        chromaClient = new ChromaClient(chromaHost, chromaPort);
        
        // 创建集合（如果不存在）
        try {
            chromaClient.createCollection(collectionName);
        } catch (Exception e) {
            // 集合可能已存在
            log.info("集合已存在或创建失败: {}", e.getMessage());
        }
    }

    public void addDocument(VectorDocument document) {
        try {
            chromaClient.add(
                collectionName,
                List.of(document.getId()),
                List.of(document.getEmbedding()),
                List.of(document.getMetadata()),
                List.of(document.getText())
            );
        } catch (Exception e) {
            log.error("向量文档添加失败", e);
            throw new VectorStoreException("向量文档添加失败", e);
        }
    }

    public List<VectorSearchResult> searchSimilar(String query, int topK) {
        try {
            // 生成查询向量
            float[] queryEmbedding = embeddingService.generateEmbedding(query);
            
            // 执行相似性搜索
            ChromaQueryResponse response = chromaClient.query(
                collectionName,
                queryEmbedding,
                topK
            );
            
            // 转换结果
            List<VectorSearchResult> results = new ArrayList<>();
            for (int i = 0; i < response.getIds().size(); i++) {
                VectorSearchResult result = VectorSearchResult.builder()
                    .id(response.getIds().get(i))
                    .text(response.getDocuments().get(i))
                    .metadata(response.getMetadatas().get(i))
                    .similarity(response.getDistances().get(i))
                    .build();
                results.add(result);
            }
            
            return results;
            
        } catch (Exception e) {
            log.error("向量搜索失败", e);
            throw new VectorStoreException("向量搜索失败", e);
        }
    }

    public float[] generateEmbedding(String text) {
        return embeddingService.generateEmbedding(text);
    }

    public void updateDocument(String documentId, VectorDocument document) {
        try {
            // ChromaDB的更新操作
            chromaClient.update(
                collectionName,
                List.of(documentId),
                List.of(document.getEmbedding()),
                List.of(document.getMetadata()),
                List.of(document.getText())
            );
        } catch (Exception e) {
            log.error("向量文档更新失败", e);
            throw new VectorStoreException("向量文档更新失败", e);
        }
    }

    public void deleteDocument(String documentId) {
        try {
            chromaClient.delete(collectionName, List.of(documentId));
        } catch (Exception e) {
            log.error("向量文档删除失败", e);
            throw new VectorStoreException("向量文档删除失败", e);
        }
    }
}
```

### 嵌入服务
```java
@Service
public class EmbeddingService {

    @Value("${training.vector-store.embedding.model}")
    private String embeddingModel;
    
    @Autowired
    private OpenAIClient openAIClient;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    public float[] generateEmbedding(String text) {
        // 检查缓存
        String cacheKey = "embedding:" + DigestUtils.md5DigestAsHex(text.getBytes());
        Object cached = redisTemplate.opsForValue().get(cacheKey);
        
        if (cached instanceof float[]) {
            return (float[]) cached;
        }
        
        try {
            // 调用OpenAI Embedding API
            EmbeddingRequest request = EmbeddingRequest.builder()
                .model(embeddingModel)
                .input(text)
                .build();
            
            EmbeddingResponse response = openAIClient.createEmbedding(request);
            float[] embedding = response.getData().get(0).getEmbedding();
            
            // 缓存结果
            redisTemplate.opsForValue().set(cacheKey, embedding, Duration.ofHours(24));
            
            return embedding;
            
        } catch (Exception e) {
            log.error("生成向量失败", e);
            throw new EmbeddingException("生成向量失败", e);
        }
    }

    public List<float[]> generateBatchEmbeddings(List<String> texts) {
        List<float[]> embeddings = new ArrayList<>();
        
        // 分批处理
        int batchSize = 100;
        for (int i = 0; i < texts.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, texts.size());
            List<String> batch = texts.subList(i, endIndex);
            
            try {
                EmbeddingRequest request = EmbeddingRequest.builder()
                    .model(embeddingModel)
                    .input(batch)
                    .build();
                
                EmbeddingResponse response = openAIClient.createEmbedding(request);
                
                for (EmbeddingData data : response.getData()) {
                    embeddings.add(data.getEmbedding());
                }
                
            } catch (Exception e) {
                log.error("批量生成向量失败", e);
                // 为失败的批次添加空向量
                for (int j = 0; j < batch.size(); j++) {
                    embeddings.add(new float[1536]); // 默认维度
                }
            }
        }
        
        return embeddings;
    }
}
```

### 质量评估器
```java
@Service
public class QualityEvaluator {

    public QualityScore evaluate(TrainingData trainingData) {
        double score = 0.0;
        List<String> issues = new ArrayList<>();
        
        // 1. SQL语法检查
        if (isValidSQL(trainingData.getSql())) {
            score += 0.3;
        } else {
            issues.add("SQL语法错误");
        }
        
        // 2. 执行结果检查
        if (trainingData.getExecutionResult() != null && trainingData.getExecutionResult().isSuccessful()) {
            score += 0.2;
        } else {
            issues.add("SQL执行失败");
        }
        
        // 3. 置信度检查
        if (trainingData.getConfidence() >= 0.8) {
            score += 0.2;
        } else if (trainingData.getConfidence() >= 0.6) {
            score += 0.1;
        } else {
            issues.add("AI置信度过低");
        }
        
        // 4. 用户反馈检查
        UserFeedback feedback = trainingData.getUserFeedback();
        if (feedback != null) {
            if (feedback.getRating() >= 4) {
                score += 0.2;
            } else if (feedback.getRating() >= 3) {
                score += 0.1;
            } else {
                issues.add("用户评分较低");
            }
        } else {
            score += 0.1; // 没有负面反馈
        }
        
        // 5. 问题质量检查
        if (isGoodQuestion(trainingData.getQuestion())) {
            score += 0.1;
        } else {
            issues.add("问题质量较低");
        }
        
        return QualityScore.builder()
            .score(Math.min(1.0, score))
            .issues(issues)
            .build();
    }

    private boolean isValidSQL(String sql) {
        try {
            // 使用SQL解析器验证语法
            CCJSqlParserUtil.parse(sql);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    private boolean isGoodQuestion(String question) {
        // 检查问题长度
        if (question.length() < 5 || question.length() > 500) {
            return false;
        }
        
        // 检查是否包含查询意图
        String lowerQuestion = question.toLowerCase();
        String[] queryKeywords = {"查询", "显示", "统计", "计算", "查找", "获取", "列出"};
        
        for (String keyword : queryKeywords) {
            if (lowerQuestion.contains(keyword)) {
                return true;
            }
        }
        
        return false;
    }
}
```

### 学习优化器
```java
@Service
public class LearningOptimizer {

    @Autowired
    private VectorStoreService vectorStoreService;
    
    @Autowired
    private TrainingDataRepository trainingDataRepository;

    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    public void optimizeTrainingData() {
        try {
            log.info("开始训练数据优化");
            
            // 1. 清理低质量数据
            cleanupLowQualityData();
            
            // 2. 更新向量索引
            updateVectorIndex();
            
            // 3. 生成质量报告
            generateQualityReport();
            
            log.info("训练数据优化完成");
            
        } catch (Exception e) {
            log.error("训练数据优化失败", e);
        }
    }

    private void cleanupLowQualityData() {
        // 删除质量评分过低的数据
        List<TrainingData> lowQualityData = trainingDataRepository
            .findByQualityScoreLessThan(0.3);
        
        for (TrainingData data : lowQualityData) {
            // 从向量存储中删除
            vectorStoreService.deleteDocument(data.getId().toString());
            
            // 从数据库中删除
            trainingDataRepository.delete(data);
        }
        
        log.info("清理了 {} 条低质量训练数据", lowQualityData.size());
    }

    private void updateVectorIndex() {
        // 重新构建向量索引以提高检索性能
        // 这里可以实现向量索引的优化逻辑
        log.info("向量索引更新完成");
    }

    private void generateQualityReport() {
        // 生成训练数据质量报告
        long totalCount = trainingDataRepository.count();
        long highQualityCount = trainingDataRepository.countByQualityScoreGreaterThanEqual(0.8);
        long mediumQualityCount = trainingDataRepository.countByQualityScoreBetween(0.5, 0.8);
        long lowQualityCount = trainingDataRepository.countByQualityScoreLessThan(0.5);
        
        log.info("训练数据质量报告 - 总数: {}, 高质量: {}, 中等质量: {}, 低质量: {}", 
            totalCount, highQualityCount, mediumQualityCount, lowQualityCount);
    }
}
```

## 🧪 测试用例

### 功能测试
- 训练数据收集测试
- 向量化存储测试
- 相似性检索测试
- 质量评估测试
- 数据清洗测试

### 性能测试
- 大规模数据处理测试
- 向量检索性能测试
- 并发访问测试
- 内存使用优化测试

### 质量测试
- 数据质量评估准确性测试
- 学习效果验证测试
- 长期性能趋势测试
- 用户满意度测试

---

**相关文档**:
- [AI查询引擎](./ai-query-engine.md)
- [MCP数据库服务](./mcp-database-service.md)
- [数据可视化系统](./data-visualization-system.md)
