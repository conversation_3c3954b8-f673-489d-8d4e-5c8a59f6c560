# AI-FDB v0.6 - MCP数据库服务

## 概述

MCP数据库服务集成Bytebase DBHub MCP服务器，提供安全、标准化的数据库访问接口。通过Model Context Protocol (MCP)协议，实现AI Agent与数据库的安全通信，支持多种数据库类型，确保查询的安全性和可靠性。

## 🎯 功能目标

- **MCP协议集成** - 标准化的AI-数据库通信协议
- **多数据库支持** - MySQL、PostgreSQL、SQL Server、MariaDB等
- **安全访问控制** - 只读模式、权限验证、SQL注入防护
- **连接池管理** - 高效的数据库连接管理
- **查询优化** - 查询缓存、性能监控、超时控制
- **错误处理** - 完善的错误检测和恢复机制

## 🏗️ 系统架构

### MCP服务架构
```
AI Agent → MCP Client → MCP Protocol → DBHub Server → Database Connection Pool → Database
    ↓         ↓          ↓             ↓              ↓                      ↓
  查询请求   协议转换    标准通信      安全验证        连接管理               数据查询
```

### 核心组件
1. **MCPClient** - MCP协议客户端
2. **DBHubConnector** - DBHub服务器连接器
3. **ConnectionPoolManager** - 数据库连接池管理器
4. **QueryExecutor** - 查询执行器
5. **SecurityGuard** - 安全防护器
6. **CacheManager** - 查询缓存管理器

## 🔧 技术实现

### DBHub MCP配置
```yaml
# application.yml
mcp:
  dbhub:
    # MCP服务器连接
    base-url: http://localhost:8080
    transport: http
    timeout: 30000
    retry-attempts: 3
    
    # 支持的数据库
    databases:
      - name: ai-fdb-mysql
        type: mysql
        dsn: "mysql://user:password@localhost:3306/ai_fdb?sslmode=disable"
        readonly: true
        max-connections: 20
        
      - name: ai-fdb-postgres
        type: postgresql
        dsn: "postgres://user:password@localhost:5432/ai_fdb?sslmode=disable"
        readonly: true
        max-connections: 15
        
    # 安全配置
    security:
      allowed-operations: ["SELECT", "SHOW", "DESCRIBE", "EXPLAIN"]
      forbidden-keywords: ["DROP", "DELETE", "UPDATE", "INSERT", "ALTER", "CREATE", "TRUNCATE"]
      max-query-complexity: 10
      enable-sql-validation: true
      max-result-rows: 10000
      query-timeout: 30s
```

### DBHub启动配置
```bash
#!/bin/bash
# start-dbhub.sh

# 启动DBHub MCP服务器（HTTP模式）
npx @bytebase/dbhub \
  --transport http \
  --port 8080 \
  --dsn "mysql://user:password@localhost:3306/ai_fdb?sslmode=disable" \
  --readonly \
  --max-connections 20 \
  --query-timeout 30s \
  --log-level info

# 或使用Docker启动
docker run --rm --init \
  --name dbhub \
  --publish 8080:8080 \
  --env DSN="mysql://user:<EMAIL>:3306/ai_fdb?sslmode=disable" \
  bytebase/dbhub \
  --transport http \
  --port 8080 \
  --readonly \
  --max-connections 20 \
  --query-timeout 30s
```

### MCP客户端实现
```java
@Service
public class MCPDatabaseService {

    @Autowired
    private MCPClient mcpClient;
    
    @Autowired
    private QueryCacheManager cacheManager;
    
    @Autowired
    private SecurityGuard securityGuard;

    public QueryExecutionResult executeQuery(DatabaseQueryRequest request) {
        try {
            // 1. 安全验证
            SecurityValidationResult validation = securityGuard.validateQuery(request.getSql());
            if (!validation.isValid()) {
                throw new SecurityException("查询安全验证失败: " + validation.getErrors());
            }
            
            // 2. 检查缓存
            String cacheKey = generateCacheKey(request);
            QueryExecutionResult cachedResult = cacheManager.get(cacheKey);
            if (cachedResult != null) {
                return cachedResult.withCacheHit(true);
            }
            
            // 3. 执行查询
            long startTime = System.currentTimeMillis();
            MCPQueryResponse response = mcpClient.executeQuery(
                request.getDatabaseName(), 
                request.getSql(),
                request.getTimeout()
            );
            long executionTime = System.currentTimeMillis() - startTime;
            
            // 4. 处理结果
            QueryExecutionResult result = processQueryResponse(response, executionTime);
            
            // 5. 缓存结果
            if (result.isSuccessful() && result.getRowCount() <= 1000) {
                cacheManager.put(cacheKey, result, Duration.ofMinutes(30));
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("MCP查询执行失败", e);
            return QueryExecutionResult.failure(e.getMessage());
        }
    }

    private QueryExecutionResult processQueryResponse(MCPQueryResponse response, long executionTime) {
        if (response.isError()) {
            return QueryExecutionResult.failure(response.getErrorMessage());
        }
        
        List<Map<String, Object>> rows = response.getRows();
        List<ColumnMetadata> columns = response.getColumns();
        
        return QueryExecutionResult.builder()
            .successful(true)
            .rows(rows)
            .columns(columns)
            .rowCount(rows.size())
            .executionTime(executionTime)
            .cacheHit(false)
            .build();
    }
}
```

### MCP协议客户端
```java
@Component
public class MCPClient {

    @Value("${mcp.dbhub.base-url}")
    private String baseUrl;
    
    @Value("${mcp.dbhub.timeout}")
    private int timeout;
    
    @Autowired
    private RestTemplate restTemplate;

    public MCPQueryResponse executeQuery(String databaseName, String sql, Duration queryTimeout) {
        try {
            // 构建MCP查询请求
            MCPQueryRequest request = MCPQueryRequest.builder()
                .method("query")
                .params(Map.of(
                    "database", databaseName,
                    "sql", sql,
                    "timeout", queryTimeout.toMillis()
                ))
                .build();
            
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("User-Agent", "AI-FDB/0.6");
            
            HttpEntity<MCPQueryRequest> entity = new HttpEntity<>(request, headers);
            
            // 发送请求
            ResponseEntity<MCPQueryResponse> response = restTemplate.exchange(
                baseUrl + "/query",
                HttpMethod.POST,
                entity,
                MCPQueryResponse.class
            );
            
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                return response.getBody();
            } else {
                return MCPQueryResponse.error("MCP服务器响应异常: " + response.getStatusCode());
            }
            
        } catch (Exception e) {
            log.error("MCP查询请求失败", e);
            return MCPQueryResponse.error("MCP查询失败: " + e.getMessage());
        }
    }

    public MCPSchemaResponse getSchema(String databaseName) {
        try {
            MCPSchemaRequest request = MCPSchemaRequest.builder()
                .method("schema")
                .params(Map.of("database", databaseName))
                .build();
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<MCPSchemaRequest> entity = new HttpEntity<>(request, headers);
            
            ResponseEntity<MCPSchemaResponse> response = restTemplate.exchange(
                baseUrl + "/schema",
                HttpMethod.POST,
                entity,
                MCPSchemaResponse.class
            );
            
            return response.getBody();
            
        } catch (Exception e) {
            log.error("获取数据库结构失败", e);
            return MCPSchemaResponse.error("获取结构失败: " + e.getMessage());
        }
    }
}
```

### 连接池管理器
```java
@Service
public class ConnectionPoolManager {

    private final Map<String, HikariDataSource> dataSources = new ConcurrentHashMap<>();
    
    @Value("${mcp.dbhub.databases}")
    private List<DatabaseConfig> databaseConfigs;

    @PostConstruct
    public void initializeDataSources() {
        for (DatabaseConfig config : databaseConfigs) {
            HikariDataSource dataSource = createDataSource(config);
            dataSources.put(config.getName(), dataSource);
        }
    }

    private HikariDataSource createDataSource(DatabaseConfig config) {
        HikariConfig hikariConfig = new HikariConfig();
        hikariConfig.setJdbcUrl(convertDSNToJdbcUrl(config.getDsn()));
        hikariConfig.setMaximumPoolSize(config.getMaxConnections());
        hikariConfig.setMinimumIdle(2);
        hikariConfig.setConnectionTimeout(30000);
        hikariConfig.setIdleTimeout(600000);
        hikariConfig.setMaxLifetime(1800000);
        hikariConfig.setReadOnly(config.isReadonly());
        
        // 设置连接属性
        hikariConfig.addDataSourceProperty("useSSL", "false");
        hikariConfig.addDataSourceProperty("serverTimezone", "UTC");
        hikariConfig.addDataSourceProperty("characterEncoding", "utf8");
        
        return new HikariDataSource(hikariConfig);
    }

    public Connection getConnection(String databaseName) throws SQLException {
        HikariDataSource dataSource = dataSources.get(databaseName);
        if (dataSource == null) {
            throw new IllegalArgumentException("未找到数据库配置: " + databaseName);
        }
        
        return dataSource.getConnection();
    }

    public DatabaseStats getStats(String databaseName) {
        HikariDataSource dataSource = dataSources.get(databaseName);
        if (dataSource == null) {
            return null;
        }
        
        HikariPoolMXBean poolBean = dataSource.getHikariPoolMXBean();
        return DatabaseStats.builder()
            .databaseName(databaseName)
            .activeConnections(poolBean.getActiveConnections())
            .idleConnections(poolBean.getIdleConnections())
            .totalConnections(poolBean.getTotalConnections())
            .threadsAwaitingConnection(poolBean.getThreadsAwaitingConnection())
            .build();
    }

    @PreDestroy
    public void closeDataSources() {
        for (HikariDataSource dataSource : dataSources.values()) {
            dataSource.close();
        }
    }
}
```

### 查询缓存管理器
```java
@Service
public class QueryCacheManager {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    private static final String CACHE_PREFIX = "mcp:query:";

    public QueryExecutionResult get(String cacheKey) {
        try {
            String key = CACHE_PREFIX + cacheKey;
            Object cached = redisTemplate.opsForValue().get(key);
            
            if (cached instanceof QueryExecutionResult) {
                return (QueryExecutionResult) cached;
            }
            
            return null;
        } catch (Exception e) {
            log.warn("查询缓存获取失败", e);
            return null;
        }
    }

    public void put(String cacheKey, QueryExecutionResult result, Duration ttl) {
        try {
            String key = CACHE_PREFIX + cacheKey;
            redisTemplate.opsForValue().set(key, result, ttl);
        } catch (Exception e) {
            log.warn("查询缓存存储失败", e);
        }
    }

    public void invalidate(String pattern) {
        try {
            Set<String> keys = redisTemplate.keys(CACHE_PREFIX + pattern);
            if (!keys.isEmpty()) {
                redisTemplate.delete(keys);
            }
        } catch (Exception e) {
            log.warn("查询缓存清理失败", e);
        }
    }

    public CacheStats getStats() {
        try {
            Set<String> keys = redisTemplate.keys(CACHE_PREFIX + "*");
            return CacheStats.builder()
                .totalKeys(keys.size())
                .hitRate(calculateHitRate())
                .memoryUsage(calculateMemoryUsage(keys))
                .build();
        } catch (Exception e) {
            log.warn("获取缓存统计失败", e);
            return CacheStats.empty();
        }
    }
}
```

## 🔒 安全控制

### SQL安全验证
- **关键词过滤** - 禁止DDL和DML操作
- **注入检测** - 检测SQL注入攻击模式
- **复杂度限制** - 限制查询复杂度和执行时间
- **结果限制** - 限制返回结果的行数

### 访问控制
- **只读模式** - 强制只读访问，禁止数据修改
- **权限验证** - 基于用户和工作空间的权限控制
- **连接限制** - 限制并发连接数和查询频率
- **审计日志** - 记录所有查询操作和结果

## 🧪 测试用例

### 功能测试
- MCP协议通信测试
- 多数据库连接测试
- 查询执行正确性测试
- 缓存机制测试
- 错误处理测试

### 性能测试
- 并发查询处理测试
- 连接池性能测试
- 查询响应时间测试
- 缓存命中率测试

### 安全测试
- SQL注入防护测试
- 权限控制测试
- 只读模式验证测试
- 恶意查询阻断测试

---

**相关文档**:
- [AI查询引擎](./ai-query-engine.md)
- [数据可视化系统](./data-visualization-system.md)
- [训练数据管理](./training-data-management.md)
