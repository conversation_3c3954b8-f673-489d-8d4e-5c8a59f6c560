# AI-FDB v0.1 - 项目初始化与用户鉴权系统

## 版本概述

v0.1版本是AI-FDB系统的基础版本，专注于项目初始化和完整的用户认证功能。本版本搭建前后端分离的基础架构，实现用户注册、登录、JWT认证等核心功能，为后续版本奠定坚实基础。

## 🎯 核心目标

- **项目基础架构搭建** - 建立完整的开发环境和项目结构
- **用户认证系统** - 实现安全可靠的用户注册、登录、权限管理
- **前后端分离** - 建立标准的API接口和前端交互模式
- **数据库设计** - 设计用户相关的核心数据表结构
- **安全机制** - 实现JWT认证、密码加密、会话管理等安全功能

## 🎯 可视化验证目标

完成v0.1版本后，用户可以：
1. **访问系统首页** - 浏览器访问 `http://localhost:3000` 看到登录页面
2. **用户注册** - 填写注册表单，创建新用户账号
3. **用户登录** - 使用用户名/邮箱和密码登录系统
4. **查看仪表板** - 登录后进入个人仪表板页面
5. **个人信息管理** - 查看和编辑个人基本信息
6. **安全退出** - 点击退出按钮，清除登录状态

## 📚 文档索引

### 详细设计文档
- [项目结构设计](project-structure.md) - 完整的项目目录结构和组织方式
- [数据库设计](database-design.md) - 用户认证相关的数据表设计
- [后端实现](backend-implementation.md) - Spring Boot后端服务详细实现
- [前端实现](frontend-implementation.md) - Vue3前端应用详细实现

### 技术规范文档
- [API接口规范](../../backend-api.md) - 后端API接口设计规范
- [数据库规范](../../database-design.md) - 数据库设计规范
- [部署指南](../../deployment/README.md) - 系统部署和运维指南



## �️ 技术栈

### 后端技术
- **Java 17** - 主要开发语言
- **Spring Boot 3.2** - 应用框架
- **Spring Security** - 安全认证框架
- **Spring Data JPA** - 数据访问层
- **MySQL 8.0** - 主数据库
- **JWT** - 无状态认证
- **Maven** - 项目构建工具

### 前端技术
- **Vue 3** - 前端框架
- **TypeScript** - 类型安全的JavaScript
- **Vite** - 构建工具
- **Element Plus** - UI组件库
- **Vue Router** - 路由管理
- **Pinia** - 状态管理
- **Axios** - HTTP客户端

## 📋 功能特性

### 用户认证功能
- ✅ **用户注册** - 支持邮箱/用户名注册，密码强度验证
- ✅ **用户登录** - 支持邮箱/用户名登录，记住登录状态
- ✅ **密码管理** - 密码加密存储，支持密码重置
- ✅ **JWT认证** - 无状态token认证，支持token刷新
- ✅ **权限控制** - 基于角色的访问控制(RBAC)
- ✅ **会话管理** - 登录状态管理，自动登出

### 用户管理功能
- ✅ **个人信息** - 查看和编辑个人基本信息
- ✅ **头像上传** - 支持头像图片上传和更新
- ✅ **账号设置** - 修改密码、邮箱等账号信息
- ✅ **登录历史** - 查看登录记录和设备信息

### 系统基础功能
- ✅ **响应式设计** - 支持桌面端和移动端访问
- ✅ **国际化支持** - 中英文界面切换
- ✅ **主题切换** - 明暗主题模式
- ✅ **错误处理** - 统一的错误处理和用户提示
- ✅ **日志记录** - 完整的操作日志和审计功能

## 🔄 版本历史

- **v0.1.0** (当前版本) - 项目初始化与用户鉴权系统
  - 完成项目基础架构搭建
  - 实现用户注册、登录、认证功能
  - 建立前后端分离架构
  - 完成基础数据库设计

## 🎯 下一步计划

v0.2版本将专注于OCR文档识别功能：
- 集成PaddleOCR引擎
- 实现文档上传和识别
- 支持多种文档格式
- 提供识别结果管理

详细计划请参考：[v0.2版本规划](../v0.2/README.md)